// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import { getThumbnailAsync } from "expo-video-thumbnails";
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from "react";
import { StyleSheet, useWindowDimensions, View } from "react-native";

import { updateLocalFile } from "@actions/local/file";
import { buildFilePreviewUrl, buildFileUrl } from "@actions/remote/file";
import CompassIcon from "@components/compass_icon";
import ProgressiveImage from "@components/progressive_image";
import VideoMetadata from "@components/video_metadata";
import { useServerUrl } from "@context/server";
import { useTheme } from "@context/theme";
import { getServerCredentials } from "@init/credentials";
import { fileExists } from "@utils/file";
import { calculateDimensions } from "@utils/images";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";

import FileIcon from "./file_icon";

import type { ImageContentFit } from "expo-image";

type Props = {
    index: number;
    file: FileInfo;
    forwardRef?: React.RefObject<unknown>;
    inViewPort?: boolean;
    isSingleImage?: boolean;
    contentFit?: ImageContentFit;
    wrapperWidth: number;
    updateFileForGallery?: (idx: number, file: FileInfo) => void;
    isFromPostList?: boolean | undefined;
    showMetadataOverlay?: boolean;
    isCurrentUser?: boolean;
    files?: FileInfo[]; // For metadata component
};

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    imagePreview: {
        ...StyleSheet.absoluteFillObject,
    },
    fileImageWrapper: {
        borderRadius: 8, // WhatsApp-style border radius
        overflow: "hidden",
    },
    boxPlaceholder: {
        paddingBottom: "100%",
    },
    failed: {
        justifyContent: "center",
        alignItems: "center",
        borderColor: changeOpacity(theme.centerChannelColor, 0.2),
        borderRadius: 4,
        borderWidth: 1,
    },
    playContainer: {
        alignItems: "center",
        justifyContent: "center",
        ...StyleSheet.absoluteFillObject,
    },
    play: {
        backgroundColor: changeOpacity(theme.sidebarText, 0.16), //changeOpacity('#000', 0.16),
        borderRadius: 20,
        height: 30,
        width: 30,
        justifyContent: "center",
        alignItems: "center",
        // paddingBottom: 10,
    },

}));

const VideoFile = ({
    index,
    file,
    forwardRef,
    inViewPort,
    isSingleImage,
    contentFit = "cover",
    wrapperWidth,
    updateFileForGallery,
    isFromPostList = true,
    showMetadataOverlay = false,
    isCurrentUser = false,
    files,
}: Props) => {
    const serverUrl = useServerUrl();
    const [failed, setFailed] = useState(false);
    const dimensions = useWindowDimensions();
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const mounted = useRef(false);
    const [video, setVideo] = useState({ ...file });

    const imageDimensions = useMemo(() => {
        // Use fixed dimensions for WhatsApp-style media containers
        const MEDIA_CONTAINER_WIDTH = 250;
        const MEDIA_CONTAINER_HEIGHT = 160;

        if (isFromPostList) {
            return {
                width: MEDIA_CONTAINER_WIDTH,
                height: MEDIA_CONTAINER_HEIGHT,
            };
        }

        // Fallback to original calculation for non-post list contexts
        if (isSingleImage) {
            const viewPortHeight =
                Math.max(dimensions.height, dimensions.width) * 0.45;
            return calculateDimensions(
                video.height || wrapperWidth,
                video.width || wrapperWidth,
                wrapperWidth,
                viewPortHeight
            );
        }
        return undefined;
    }, [
        dimensions.height,
        dimensions.width,
        video.height,
        video.width,
        wrapperWidth,
        isSingleImage,
        isFromPostList,
    ]);

    const getThumbnail = async () => {
        const data = { ...file };
        try {
            const exists = data.mini_preview
                ? await fileExists(data.mini_preview)
                : false;
            if (!data.mini_preview || !exists) {
                const videoUrl = buildFileUrl(serverUrl, data.id!);
                if (videoUrl) {
                    const cred = await getServerCredentials(serverUrl);
                    const headers: Record<string, string> = {};
                    if (cred?.token) {
                        headers.Authorization = `Bearer ${cred.token}`;
                    }
                    const { uri, height, width } = await getThumbnailAsync(
                        `${serverUrl}/api/v4/files/${file.id}`,
                        // data.localPath || videoUrl
                        { time: 1000, headers }
                    );
                    data.mini_preview = uri;
                    data.height = height;
                    data.width = width;
                    updateLocalFile(serverUrl, data);
                    if (mounted.current) {
                        setVideo(data);
                        setFailed(false);
                    }
                }
            }
        } catch (error) {
            data.mini_preview = buildFilePreviewUrl(serverUrl, data.id!);
            if (mounted.current) {
                setVideo(data);
            }
        } finally {
            if (!data.width) {
                data.height = wrapperWidth;
                data.width = wrapperWidth;
            }
            const { width: tw, height: th } = calculateDimensions(
                data.height,
                data.width,
                dimensions.width - 60, // size of the gallery header probably best to set that as a constant
                dimensions.height
            );
            data.height = th;
            data.width = tw;
            updateFileForGallery?.(index, data);
        }
    };

    const handleError = useCallback(() => {
        setFailed(true);
    }, []);

    useEffect(() => {
        mounted.current = true;
        return () => {
            mounted.current = false;
        };
    }, []);

    useEffect(() => {
        if (inViewPort) {
            getThumbnail();
        }
    }, [file, inViewPort]);

    const imageProps = () => {
        const props: ProgressiveImageProps = {
            imageUri: video.mini_preview,
            inViewPort,
        };

        return props;
    };

    let thumbnail = (
        <ProgressiveImage
            id={file.id!}
            forwardRef={forwardRef}
            style={[
                isSingleImage ? null : style.imagePreview,
                isFromPostList ? imageDimensions : { height: 60, width: 60 },
            ]}
            onError={handleError}
            contentFit={contentFit}
            {...imageProps()}
        />
    );

    if (failed) {
        thumbnail = (
            <View
                // @ts-expect-error ref of type unknown
                ref={forwardRef}
                style={[
                    isSingleImage ? null : { height: "100%" },
                    style.failed,
                    isFromPostList
                        ? imageDimensions
                        : { height: 60, width: 60 },
                ]}
            >
                <FileIcon failed={failed} file={file} />
            </View>
        );
    }

    return (
        <View style={style.fileImageWrapper}>
            {!isSingleImage && !failed && <View style={style.boxPlaceholder} />}
            {isFromPostList && thumbnail}
            {
                <View
                    style={[
                        isFromPostList ? style.playContainer : style.play,
                        { marginTop: 1.7, marginStart: 2 },
                    ]}
                >
                    <View
                        style={
                            isFromPostList && {
                                backgroundColor: changeOpacity("black", 0.41),
                                borderRadius: 20,
                                height: 30,
                                width: 30,
                                justifyContent: "center",
                                alignItems: "center",
                            }
                        }
                    >
                        <View
                            style={{
                                start: 0,
                                backgroundColor: changeOpacity(
                                    theme.centerChannelBg,
                                    1
                                ),
                                height: 50,
                                width: 50,
                                borderRadius: 30,
                                justifyContent: "center",
                                alignItems: "center",
                            }}
                            >
                            <CompassIcon
                                color={changeOpacity(
                                    theme.centerChannelColor,
                                    0.7
                                )}
                            name="play"
                            size={40}
                        />
                        </View>
                    </View>
                </View>
            }
            {/* Video Metadata Overlay */}
            {showMetadataOverlay && files && isFromPostList && (
                <VideoMetadata
                    files={files}
                    isCurrentUser={isCurrentUser}
                    asOverlay={true}
                />
            )}
        </View>
    );
};

export default VideoFile;
