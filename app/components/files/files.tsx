// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { DeviceEventEmitter, Dimensions, type StyleProp, StyleSheet, View, type ViewStyle } from 'react-native';
import Animated, { useDerivedValue } from 'react-native-reanimated';

import { Events } from '@constants';
import { GalleryInit } from '@context/gallery';
import { useIsTablet } from '@hooks/device';
import { useImageAttachments } from '@hooks/files';
import { isImage, isVideo } from '@utils/file';
import { fileToGalleryItem, openGalleryAtIndex } from '@utils/gallery';
import { getViewPortWidth } from '@utils/images';
import { preventDoubleTap } from '@utils/tap';

import File from './file';
import type { GalleryAction } from '@typings/screens/gallery';
import FileInfo from './file_info';
import theme, { useTheme } from '@app/context/theme';
import { changeOpacity } from '@app/utils/theme';
import { AudioProvider } from '@app/context/AudioPlayController';
type FilesProps = {
    canDownloadFiles: boolean;
    failed?: boolean;
    filesInfo: FileInfo[];
    layoutWidth?: number;
    location: string;
    isReplyPost: boolean;
    postId: string;
    postProps: Record<string, any>;
    publicLinkEnabled: boolean;
    isCurrentUser?: boolean | null
    isHasMessage?: boolean | undefined
    acknolowgmentAudio?: React.JSX.Element | undefined
    acknowledgementsFile?: React.JSX.Element | undefined
    acknowledgementsImage?: React.JSX.Element | undefined
    audioTimerComponent?: React.JSX.Element | undefined
    currentPostTime?: number | undefined
    setLastViewedFileInfo?: (fileINfo: FileInfo) => void | undefined;
    author?: any; // User model for voice message avatar
    showMetadataOverlay?: boolean;
    metadataOverlayContent?: React.ReactNode;
    files?: FileInfo[]; // For video metadata overlay
}

const MAX_VISIBLE_ROW_IMAGES = 2;
const styles = StyleSheet.create({
    row: {
        flex: 1,
        flexDirection: 'row',
        marginTop: 0,
        gap: 0,
        justifyContent: 'flex-start',
        borderRadius: 12,
        paddingTop: 0,
        paddingBottom: 5,
        paddingLeft: 5,
        paddingRight: 5,
        flexWrap: 'wrap',
        height: 160,
    },
    container: {
        flex: 1,
        flexGrow: 1,
        flexShrink: 1,
        flexBasis: '47%',
        height: 110,
    },
    gutter: {
        marginLeft: 3,
    },
    failed: {
        opacity: 0.5,
    },
    marginTop: {
        marginTop: 5
    },
});







const Files = ({
    canDownloadFiles,
    failed,
    filesInfo,
    isReplyPost,
    layoutWidth,
    location,
    postId,
    postProps,
    publicLinkEnabled,
    isCurrentUser = false,
    audioTimerComponent = undefined,
    acknolowgmentAudio = undefined,
    acknowledgementsFile = undefined
    , acknowledgementsImage = undefined
    , currentPostTime = undefined,
    setLastViewedFileInfo = undefined,
    isHasMessage=false,
    author = undefined,
    showMetadataOverlay = false,
    metadataOverlayContent = undefined,
    files = undefined

}: FilesProps) => {




    const windowWidth = Dimensions.get('window').width;




    const galleryIdentifier = `${postId}-fileAttachments-${location}`;

    const [inViewPort, setInViewPort] = useState(false);

    const isTablet = useIsTablet();
    const { images: imageAttachments, nonImages: nonImageAttachments } = useImageAttachments(filesInfo, publicLinkEnabled);

    const filesForGallery = useDerivedValue(() => imageAttachments.concat(nonImageAttachments),
        [imageAttachments, nonImageAttachments]);

    const attachmentIndex = (fileId: string) => {
        return filesForGallery.value.findIndex((file) => file.id === fileId) || 0;
    };

    const handlePreviewPress = preventDoubleTap((idx: number) => {
        const items = filesForGallery.value.map((f) => fileToGalleryItem(f, f.user_id, postProps));
        // Debug log removed to prevent React Native text rendering errors
        openGalleryAtIndex(galleryIdentifier, idx, [items[idx]]);
        // openGalleryAtIndex(galleryIdentifier, idx, items );
        
    });

    const updateFileForGallery = (idx: number, file: FileInfo) => {
        'worklet';
        filesForGallery.value[idx] = file;
    };

    const isSingleImage = useMemo(() => filesInfo.filter((f) => isImage(f) || isVideo(f)).length === 1, [filesInfo]);

    const renderItems = (items: FileInfo[], moreImagesCount = 0, includeGutter = false , isImage = false) => {
        const theme = useTheme()
        let nonVisibleImagesCount: number;
        let container: StyleProp<ViewStyle> = items.length > 1 ? styles.container : undefined;
        const containerWithGutter = [container, styles.gutter];

        return items.map((file, idx) => {

            if (moreImagesCount && idx === MAX_VISIBLE_ROW_IMAGES - 1) {
                nonVisibleImagesCount = moreImagesCount;
            }

            if (idx !== 0 && includeGutter) {
                container = containerWithGutter;
            }
            // console.log(`\n\nis single images ${isSingleImage} = = == = = = = = \n\n`)

            return (

                <View
                    style={[
                         isImage&&container,
                        {
                            width:nonVisibleImagesCount>1? 70:undefined,
                           // borderWidth:isHasMessage?undefined: 1,
                            borderRadius: 8, // WhatsApp-style border radius
                            overflow: 'hidden',
                            //borderColor: theme.centerChannelBg
                            alignItems:'center'
                        },
                       // styles.marginTop
                    ]}
                    key={file.id}
                >
                    <File
                            isMenyFile={items.length>1}
                        isHasMessage={isHasMessage}
                        acknolowgmentImage={acknowledgementsImage}
                        setLastViewedFileInfo={setLastViewedFileInfo}
                        currentPostTime={currentPostTime}
                        audioTimerComponent={audioTimerComponent}
                        acknolowgmentFile={acknowledgementsFile}
                        acknolowgmentAudio={acknolowgmentAudio}
                        galleryIdentifier={galleryIdentifier}
                        canDownloadFiles={canDownloadFiles}
                        file={file}
                        index={attachmentIndex(file.id || '')}
                        onPress={handlePreviewPress}
                        isSingleImage={isSingleImage}
                        nonVisibleImagesCount={nonVisibleImagesCount}
                        publicLinkEnabled={publicLinkEnabled}
                        updateFileForGallery={updateFileForGallery}
                        wrapperWidth={layoutWidth || (getViewPortWidth(isReplyPost, isTablet) - 6)}
                        inViewPort={inViewPort}
                        isCurrentUser={isCurrentUser}
                        author={author}
                    />
                </View>
            );
        });
    };
    const renderImageRow = () => {
        const theme = useTheme()
        if (imageAttachments.length === 0) {
            return null;
        }

        const visibleImages = imageAttachments.slice(0, imageAttachments.length);
        const portraitPostWidth = layoutWidth || (getViewPortWidth(isReplyPost, isTablet) - 6);

        let nonVisibleImagesCount;
        if (imageAttachments.length > MAX_VISIBLE_ROW_IMAGES) {
            nonVisibleImagesCount = imageAttachments.length - 1 //- MAX_VISIBLE_ROW_IMAGES;
        }
        // Debug log removed to prevent React Native text rendering errors
        return (

            <View style={[styles.row, {
                maxWidth: (windowWidth / 4) * 3
            }, isSingleImage ? null : {
                //borderWidth: 2,
               // borderColor: isCurrentUser ? theme.buttonBg : changeOpacity(theme.sidebarText, 0.16)
            }]}>
                {renderItems(visibleImages, nonVisibleImagesCount, true,true)}
            </View>

        );
    };

    useEffect(() => {
        const onScrollEnd = DeviceEventEmitter.addListener(Events.ITEM_IN_VIEWPORT, (viewableItems) => {
            if (`${location}-${postId}` in viewableItems) {
                setInViewPort(true);
            }
        });

        return () => onScrollEnd.remove();
    }, []);


    return (
        // <AudioProvider>

        <GalleryInit galleryIdentifier={galleryIdentifier}>
            <Animated.View style={[failed && styles.failed]}>
                {
                    renderImageRow()
                }
                {
                    renderItems(nonImageAttachments)
                }
            </Animated.View>
        </GalleryInit>
                // </AudioProvider>

    );
};

export default React.memo(Files);


// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
