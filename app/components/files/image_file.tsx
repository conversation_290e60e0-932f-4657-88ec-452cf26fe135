// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import { LinearGradient } from 'expo-linear-gradient';
import React, { useCallback, useMemo, useState } from 'react';
import { Dimensions, StyleSheet, useWindowDimensions, View } from 'react-native';

import { buildFilePreviewUrl, buildFileThumbnailUrl, buildFileUrl } from '@actions/remote/file';
import CompassIcon from '@components/compass_icon';
import ProgressiveImage from '@components/progressive_image';
import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { isGif as isGifImage } from '@utils/file';
import { calculateDimensions } from '@utils/images';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';

import FileIcon from './file_icon';

import type { ImageContentFit } from 'expo-image';
import MessageCornerSvg from '../post_list/post/body/message/message_corner_svg';
import { back } from 'nock';
import { prop } from 'lodash/fp';

type ImageFileProps = {
    backgroundColor?: string;
    file: FileInfo;
    forwardRef?: React.RefObject<unknown>;
    inViewPort?: boolean;
    isSingleImage?: boolean;
    contentFit?: ImageContentFit;
    wrapperWidth?: number;
    isCurrentUser?: boolean | null;
    isFromPostList?: boolean | null
    isLocal?: boolean | undefined;
    isHasMessage?: boolean | undefined;
    showMetadataOverlay?: boolean;
    metadataOverlayContent?: React.ReactNode; // Custom overlay content for timestamps/receipts
}

const SMALL_IMAGE_MAX_HEIGHT = 38;
const SMALL_IMAGE_MAX_WIDTH = 48;
const GRADIENT_COLORS = ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, .32)'];
const GRADIENT_END = { x: 1, y: 1 };
const GRADIENT_LOCATIONS = [0.5, 1];
const GRADIENT_START = { x: 0.5, y: 0.5 };

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    boxPlaceholder: {
        paddingBottom: '100%',
    },
    fileImageWrapper: {
        borderRadius: 8,
        overflow: 'hidden',
    },
    failed: {
        justifyContent: 'center',
        alignItems: 'center',
        borderColor: changeOpacity(theme.centerChannelColor, 0.2),
        borderRadius: 4,
        borderWidth: 1,
    },
    gifContainer: {
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
        padding: 8,
        ...StyleSheet.absoluteFillObject,
    },
    imagePreview: {
        ...StyleSheet.absoluteFillObject,
    },
    singleSmallImageWrapper: {
        height: SMALL_IMAGE_MAX_HEIGHT,
        width: SMALL_IMAGE_MAX_WIDTH,
    },
}));

const ImageFile = ({
    backgroundColor, file, forwardRef, inViewPort, isSingleImage,
    contentFit = 'cover', wrapperWidth, isFromPostList = false, isLocal = undefined, isCurrentUser, isHasMessage = false,
    showMetadataOverlay = false, metadataOverlayContent
}: ImageFileProps) => {
    const dimensions = useWindowDimensions();
    const theme = useTheme();
    const serverUrl = useServerUrl();
    const isGif = useMemo(() => isGifImage(file), [file]);
    const [failed, setFailed] = useState(false);
    const style = getStyleSheet(theme);
    let image;
    const windowWidth = Dimensions.get('window').width;
    const defaultWidth = windowWidth < 400 ? 290 : 306;



    const getImageDimensions = () => {
        // Use fixed dimensions for WhatsApp-style media containers
        const MEDIA_CONTAINER_WIDTH = defaultWidth - 50;
        const MEDIA_CONTAINER_HEIGHT = 160;

        if (isFromPostList) {
            return {
                width: MEDIA_CONTAINER_WIDTH,
                height: MEDIA_CONTAINER_HEIGHT
            };
        }

        // Fallback to original calculation for non-post list contexts
        if (isSingleImage) {
            const viewPortHeight = Math.max(dimensions.height, dimensions.width) * 0.45;
            return calculateDimensions(file?.height, file?.width > (dimensions.width - (dimensions.width / 3)) ? 200 : 100, wrapperWidth, viewPortHeight);
        }

        return undefined;
    };

    const handleFileHeighAndWidth = () => {
        const screanWidthMiddle = dimensions.width / 2.05;
        const fileHeight = file.height | 0, fileWidth = file.width;
        // if (fileHeight + fileHeight < 100) {
        //     return { height: 100, width: isHasMessage?(windowWidth / 4) * 3: 100 }
        // }
        // else if (fileHeight > fileWidth) {
        //     return { height: 350, width: isHasMessage?(windowWidth / 4) * 3: fileWidth > screanWidthMiddle ? (screanWidthMiddle + (screanWidthMiddle / 2.5)) : fileWidth }

        // }
        // else if (fileHeight < fileWidth) {
        //     return { height: 250, width: isHasMessage? (windowWidth / 4) * 3: fileWidth > screanWidthMiddle ? (screanWidthMiddle + (screanWidthMiddle / 2.5)) : fileWidth }

        // }
        // else {

        return { height: 160, paddingTop: width: defaultWidth - 50 }

        // }


    }

    const handleError = useCallback(() => {
        setFailed(true);
    }, []);

    const imageProps = () => {
        const props: ProgressiveImageProps = {};

        // if (file.localPath) {
        //     const prefix = file.localPath.startsWith('file://') ? '' : 'file://';
        //     const realDeviceUrl = `${prefix + file.localPath}`.replace("file:///data/user/0", "file:///data/data")
        //     console.log(`\n\nthis shown the localfile path ${realDeviceUrl}\n\n`)

        //     props.defaultSource = { uri: prefix + file.localPath };
        // } else
        
        if (file.id) {
            if (file.mini_preview && file.mime_type) {
                props.thumbnailUri = `data:${file.mime_type};base64,${file.mini_preview}`;
            } else if (file.has_preview_image) {
                props.thumbnailUri = buildFileThumbnailUrl(serverUrl, file.id);
            }
            if (file.has_preview_image) {
                props.imageUri = buildFilePreviewUrl(serverUrl, file.id);
            } else {
                props.imageUri = buildFileUrl(serverUrl, file.id, file.update_at);
            }
            props.inViewPort = inViewPort;
        }

        return props;
    };

    let imageDimensions = getImageDimensions();
    if (isSingleImage && (!imageDimensions || (imageDimensions?.height === 0 && imageDimensions?.width === 0))) {
        imageDimensions = style.singleSmallImageWrapper;
    }


    image = (
        <ProgressiveImage
            isFromPostList={isFromPostList}
            id={file.id!}
            forwardRef={forwardRef}
            style={[isSingleImage ? null : style.imagePreview, {
                overflow: 'hidden',


            }, isFromPostList ? handleFileHeighAndWidth() : { height: 60, width: 60 }]}
            tintDefaultSource={!file.localPath && !failed}
            onError={handleError}
            contentFit={contentFit}
            {...imageProps()}
        />
    );

    if (failed) {
        image = (
            <View style={[isSingleImage ? null : style.imagePreview, style.failed, {

            }]}>
                <FileIcon
                    failed={failed}
                    file={file}
                    backgroundColor={backgroundColor}
                />
            </View>
        );
    }

    let gifIndicator;
    if (isGif) {
        gifIndicator = (
            <View style={StyleSheet.absoluteFill}>
                <LinearGradient
                    start={GRADIENT_START}
                    end={GRADIENT_END}
                    locations={GRADIENT_LOCATIONS}
                    colors={GRADIENT_COLORS}
                    style={[style.imagePreview, {

                        overflow: 'hidden'
                        ,
                    }, handleFileHeighAndWidth()]}
                />
                <View style={[style.gifContainer, {

                    overflow: 'hidden'
                    ,
                }, handleFileHeighAndWidth()]}>
                    <CompassIcon
                        name='file-gif'
                        color='#FFF'
                        size={24}
                    />
                </View>
            </View>
        );
    }

    return (
        <View
            style={[style.fileImageWrapper, !isHasMessage && {
                borderRadius: 5
            }]}
        >
            {!isSingleImage && <View style={style.boxPlaceholder} />}
            {
                image
            }
            {gifIndicator}
            {/* Metadata Overlay for timestamps/receipts */}
            {showMetadataOverlay && metadataOverlayContent && isFromPostList && (
                <View style={{
                    position: 'absolute',
                    bottom: 8,
                    left: 0,
                    right: 0,
                    paddingHorizontal: 12,
                    zIndex: 10,
                }}>
                    {metadataOverlayContent}
                </View>
            )}
        </View>
    );
};

export default ImageFile;
