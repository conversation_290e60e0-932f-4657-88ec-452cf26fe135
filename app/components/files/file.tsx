// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { View, TouchableWithoutFeedback, Dimensions, Pressable, Text, StyleSheet, Image, useWindowDimensions } from 'react-native';
import Animated, { useSharedValue } from 'react-native-reanimated';

import TouchableWithFeedback from '@components/touchable_with_feedback';
import { useTheme } from '@context/theme';
import { useGalleryItem } from '@hooks/gallery';
import { fileExists, getLocalFilePathFromFile, hasWriteStoragePermission, isDocument, isImage, isVideo } from '@utils/file';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';

import DocumentFile from './document_file';
import FileIcon from './file_icon';
import FileInfo from './file_info';
import FileOptionsIcon from './file_options_icon';
import ImageFile from './image_file';
import ImageFileOverlay from './image_file_overlay';
import VideoFile from './video_file';

import type { DocumentRef } from '@components/document';
import { Sound } from "expo-av/build/Audio/Sound";
import AudioFile from './audio_file';
import { Audio } from 'expo-av';
import { F, path } from 'lodash/fp';
import { ArrowDownTrayIcon } from 'react-native-heroicons/mini'
import Time from '@app/utils/TimeHelper';
import type { GalleryAction, GalleryItemType } from '@typings/screens/gallery';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useServerUrl } from '@app/context/server';
import { AnimatedImage } from 'react-native-reanimated/lib/typescript/component/Image';
import { SvgXml } from 'react-native-svg';
import { calculateDimensions } from '@app/utils/images';
import { Screens } from '@app/constants';
import { goToScreen } from '@app/screens/navigation';
import { typography } from '@app/utils/typography';
import { isAudioEnabled } from 'expo-av/build/Audio/AudioAvailability';
import CustomCircularProgress from '../custom_circler_progress/customCirlclerProgress';
import useDownloadPrecentage from '@app/controller/downloadPrecentage';
import RNFS from 'react-native-fs';
import { XMarkIcon } from 'react-native-heroicons/outline';
import type { ClientResponse, ProgressPromise } from '@mattermost/react-native-network-client';
import { fileToGalleryItem, galleryItemToFileInfo } from '@app/utils/gallery';
import { downloadProfileImage, downloadFile } from '@actions/remote/file';
import { useIntl } from 'react-intl';
import { deleteAsync } from 'expo-file-system';
import DirectoryUtil from '@app/utils/directory';
import { showSnackBar } from '@app/utils/snack_bar';
import { SNACK_BAR_TYPE } from '@app/constants/snack_bar';
import DeviceInfo from 'react-native-device-info';
import { updateLocalFile, updateLocalFilePath } from '@actions/local/file';
import { TouchableOpacity } from 'react-native-gesture-handler';

//import {BlurView} from 'expo-blur';
type FileProps = {
    canDownloadFiles: boolean;
    file: FileInfo;
    galleryIdentifier: string;
    index: number;
    inViewPort: boolean;
    isSingleImage?: boolean;
    nonVisibleImagesCount: number;
    onPress: (index: number) => void;
    publicLinkEnabled: boolean;
    channelName?: string;
    onOptionsPress?: (fileInfo: FileInfo) => void;
    clearDownload?: (fileInfo: FileInfo) => void;
    optionSelected?: boolean;
    wrapperWidth?: number;
    showDate?: boolean;
    updateFileForGallery: (idx: number, file: FileInfo) => void;
    asCard?: boolean;
    isPressDisabled?: boolean;
    isCurrentUser?: boolean | null;
    isFromSearch?: boolean | null;
    acknolowgmentFile?: React.JSX.Element | undefined;
    acknolowgmentAudio?: React.JSX.Element | undefined;
    acknolowgmentImage?: React.JSX.Element | undefined;
    audioTimerComponent?: React.JSX.Element | undefined
    currentPostTime?: number | undefined
    setLastViewedFileInfo?: (fileInfo: FileInfo) => void | undefined;
    isHasMessage?: boolean | undefined;
    progressValue?: number | undefined;
    isMenyFile?: boolean | undefined;
    author?: any; // User model for voice message avatar
};

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        fileWrapper: {
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',

        },
        fileWrapperForAudio: {
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            // borderWidth: 1,
            // borderColor: changeOpacity(theme.centerChannelColor, 0.24),
            // borderRadius: 15,
        },
        iconWrapper: {
            marginTop: 4,
            marginRight: 7,
            // marginBottom: 4,
            marginLeft: 6,
        },
        imageVideo: {
            height: 40,
            width: 40,
            margin: 4,
        },
    };
});

const File = ({
    asCard = false,
    canDownloadFiles,
    channelName,
    file,
    galleryIdentifier,
    inViewPort = false,
    index,
    isSingleImage = false,
    nonVisibleImagesCount = 0,
    onOptionsPress,
    onPress,
    optionSelected,
    publicLinkEnabled,
    showDate = false,
    updateFileForGallery,
    wrapperWidth = 300,
    isPressDisabled = false,
    isCurrentUser = false,
    isFromSearch = false,
    acknolowgmentFile = undefined,
    acknolowgmentAudio = undefined,
    acknolowgmentImage = undefined,
    audioTimerComponent = undefined,
    currentPostTime = undefined,
    setLastViewedFileInfo = undefined,
    isHasMessage = false,
    clearDownload = undefined,
    progressValue = undefined,
    isMenyFile = false,
    author = undefined
}: FileProps) => {
    //console.log(`\n\nthis shown the file path ${JSON.stringify(file)}\n\n`)
    // console.log(`\n\nthis shown the file path ${file.localPath}\n\n`)

    const { prcentage, clearePrecentageList, setPrecentage } = useDownloadPrecentage()
    const [isAudio] = useState((file?.mime_type?.split("/")[0] === "audio") || false)

    // Helper function to detect voice messages
    const isVoiceMessage = useMemo(() => {
        return isAudio && (
            file?.name?.toLowerCase().includes('voice') ||
            file?.name?.toLowerCase().includes('recording') ||
            file?.mime_type?.includes('m4a') ||
            file?.mime_type?.includes('aac') ||
            file?.mime_type?.includes('opus')
        );
    }, [isAudio, file?.name, file?.mime_type]);


    const windowWidth = Dimensions.get('window').width;

    const document = useRef<DocumentRef>(null);
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const dimensions = useWindowDimensions();
    const isSupported = useMemo(() => isDocument(file), [file]);
    const fileID = file.id
    // const handlePreviewPress = useCallback(() => {
    //     // updateFileForGallery(index,file)
    //     // if (document.current) 
    //     //     document.current.handlePreviewPress();
    //     handleOnOptionsPress
    //     // } else {
    //   //  onPress(index);
    //     // }
    // }, [index]);

    const handlePreviewPress = useCallback(() => {
        // if (document.current) {
        //     document.current.handlePreviewPress();
        // } else {
        onPress(index);
        // }
    }, [index]);


    const {
        //styles, 
        onGestureEvent, ref } = useGalleryItem(galleryIdentifier, index, handlePreviewPress);
    // const handleOnOptionsPress = useCallback(() => {
    //     onOptionsPress?.(file);
    // }, [file, onOptionsPress]);


    const prcentageValue = useSharedValue(0)

    const serverUrl = useServerUrl();
    const [showToast, setShowToast] = useState<boolean | undefined>();
    const [error, setError] = useState('');
    const [saved, setSaved] = useState(false);
    const [progress, setProgress] = useState(0);

    const mounted = useRef(false);
    const downloadPromise = useRef<ProgressPromise<ClientResponse>>();

    const [action, setAction] = useState<GalleryAction>('downloading');

    const galerFile = fileToGalleryItem(file);


    const reNameFileIfNotEmulator = async (fileName: string) => {
        let newPath = fileName;
        if (await DeviceInfo.isEmulator()) {
            let tempPath = newPath;
            newPath = 'data/data' + newPath.substring('/data/user/0'.length, newPath.length)
        }
        return newPath;
    }

    const startDownloadForFileInfo = async () => {
        try {
            // if(prcentage.some((item) => item.id === fileInfo.id))
            // await cancelForFileInfo(undefined);

            if (file.id) {
                setPrecentage(0, file.id);
            }

            // console.log(`\n\nthis from start download function  =====\n\n`);


            const path = await getLocalFilePathFromFile(serverUrl, galleryItemToFileInfo(galerFile));  // Make this async
            const hasPermission = await hasWriteStoragePermission(intl);

            let newPath = await reNameFileIfNotEmulator(path)
            if (!hasPermission) {
                cancelForFileInfo;
                return;
            }
            if (path) {
                // Ensure async operations here
                const exists = await fileExists(newPath); // Make this async

                let actionToExecute: (response: ClientResponse, itemss: GalleryItemType) => Promise<void>;
                switch (action) {
                    default:
                        actionToExecute = saveFileInfo;
                        break;
                }
                // console.log(`\n\nthis from start download function after switch  =====\n\n`);

                if (exists) {
                    setProgress(0);
                    // setProgress(20);
                    //  setProgress(30);
                    //  setProgress(35);
                    setProgress(5);
                    actionToExecute(
                        {
                            code: 200,
                            ok: true,
                            data: { newPath },
                        },
                        galerFile
                    );
                    setProgress(100);
                    // console.log(`\n\nthis from start download function file exist =====\n\n`);
                } else {
                    // Trigger download in the background
                    downloadPromise.current = galerFile.type === "avatar"
                        ? downloadProfileImage(serverUrl, galerFile.id || '', galerFile.lastPictureUpdate, path)
                        : downloadFile(serverUrl, galerFile.id || '', path);

                    // Handle progress update asynchronously
                    downloadPromise.current?.then((data) => {
                        actionToExecute(data, galerFile);
                    }).catch((error) => {
                        setError(
                            intl.formatMessage({
                                id: 'download.error',
                                defaultMessage: 'Unable to download the file. Try again later',
                            })
                        );
                    });

                    // Offload progress updates so it doesn't block the thread
                    if (downloadPromise.current?.progress) {
                        downloadPromise.current.progress(setProgress);
                    }
                }
            }
        } catch (e) {
            // console.log(`\n\nthis the error from download file on sofa ${e}\n\n`);
        }
    };


    const saveFileInfo = async (response: ClientResponse, itemss: GalleryItemType) => {
        if (response.data?.path) {
            const path = response.data.path as string;


            const hasPermission = await hasWriteStoragePermission(intl);
            // console.log(`\n\nthis the permssion result ${hasPermission}\n\n`)
            const fileDownloadUtil = new DirectoryUtil()
            if (hasPermission) {
                switch (itemss.type) {
                    case 'file':
                        fileDownloadUtil
                            .createDirectoryIfIsNotExist(
                                path,
                                itemss
                            );
                        // saveFile(path)
                        break;
                    default:
                        fileDownloadUtil
                            .createDirectoryIfIsNotExist(
                                path,
                                itemss
                            );
                        break;
                }
            }
        }
    };
    const cancelForFileInfo = async () => {
        try {


            if (file === undefined) {
                downloadPromise.current?.cancel?.();
                return;
            }

            if (file.id) {
                clearePrecentageList(file.id);
            }


            downloadPromise.current?.cancel?.();
            const path = getLocalFilePathFromFile(serverUrl, galleryItemToFileInfo(galerFile));
            downloadPromise.current = undefined;
            let newPath = await reNameFileIfNotEmulator(path)

            await deleteAsync(newPath);
        } catch {
            // do nothing
        } finally {
            if (mounted.current) {
                setShowToast(false);
            }
        }
    };

    const intl = useIntl();





    useEffect(() => {

        if (progress !== 0 && prcentage.length > 0) {
            setPrecentage(progress, prcentage[0].id)
        }

        if (progress === 1 || progress === 100) {
            if (file.id) {
                clearePrecentageList(file.id);
                updateLocalFilePath(serverUrl, file.id || '', "newPath");

            }
            // console.log(`\n\nthis the progress ${progress}\n\n`)
        }

    }, [progress])


    const renderCardWithImage = (fileIcon: JSX.Element, isDocumentss = false) => {

        const fileInfo = (
            <>
                {isAudio ?

                    <View
                        style={{
                            // flex: 1, marginEnd: 55,
                            width: isFromSearch ? windowWidth - 40 : 290,
                            start: -7,
                            top: isFromSearch ? 16 : 15
                        }}>

                        <AudioFile
                            key={file.id}
                            isCurrentUser={isCurrentUser ?? undefined}
                            acknolowgment={acknolowgmentAudio}
                            disabled={isPressDisabled}
                            file={file}
                            author={author}
                        />
                    </View>
                    :
                    <View
                        style={{
                            flex: 1,
                            marginEnd: 5,
                            width: windowWidth - 100,
                        }}
                    >

                        <FileInfo
                            acknolowgmentFile={acknolowgmentFile}
                            isFromSearch={isFromSearch || false} disabled={isPressDisabled}
                            file={file}
                            showDate={showDate}
                            channelName={channelName}
                            onPress={handlePreviewPress}
                            isCurrentUser={isCurrentUser}
                        />
                    </View>
                }
            </>
        );

        const fileIconHolder = () => {
            if (isDocument(file) || isVideo(file)) {
                return <View style={style.iconWrapper}>
                    <DocumentFile

                        ref={document}
                        canDownloadFiles={canDownloadFiles}
                        disabled={isPressDisabled}
                        file={file}
                    />
                </View>
            }
            if (isImage(file)) {


                return <Animated.View
                    style={[
                        // styles,
                        style.imageVideo]}
                >


                    <Pressable
                        onPress={onGestureEvent}
                        style={{
                            position: 'absolute',
                            backgroundColor: isCurrentUser !== undefined ? changeOpacity(theme.sidebarText, 0.30) : undefined,
                            height: 40, width: 40, top: 0, start: 0, end: 0, bottom: 0, zIndex: 10,
                            opacity: 1,
                            borderRadius: 0
                        }} />
                    <ImageFile
                        isFromPostList={true}
                        file={file}
                        forwardRef={ref}
                        inViewPort={inViewPort}
                        isSingleImage={false}
                        contentFit={'cover'}
                        wrapperWidth={wrapperWidth}
                        isCurrentUser={isCurrentUser}
                    />
                    {//Boolean(nonVisibleImagesCount) &&
                        //  <ImageFileOverlay
                        //      value={1}
                        //  />
                    }
                </Animated.View>

            }
            else return null
        }


        return (
            <View style={{ flexDirection: 'column' }}>
                <View style={!isFromSearch ? {
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 5,

                } : {

                    display: 'flex',
                    flexDirection: isFromSearch ? 'row' : 'column',
                    alignItems: 'center',

                }}>




                    {<View style={{
                        position: 'relative',
                        height: 60,
                        width: 50,
                        borderRadius: 8,
                        alignItems: 'center'
                        , justifyContent: 'center',
                        marginStart: 5,
                        marginEnd: 5,
                        top: isAudio ? 1 : 2.5,
                      
                    }}>

                        <FileIcon
                            file={file}
                            iconSize={70}
                            isFromUpload={false}
                            backgroundColor={'transparent'}
                        />
                         {!prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={() => {
                                    // clearePrecentageList();
                                    startDownloadForFileInfo()
                                    // console.log(`\n\n\nthis the start download call ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: 5,
                                    flexDirection: 'row',
                                    position: 'relative',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    overflow: 'hidden'
                                }}
                            >



                                <Text
                                    style={{
                                        color: 'white',
                                        fontSize: 12,
                                        textAlign:'center',
                                    }}
                                numberOfLines={1}
                                >
                                    {`${file.extension?.toUpperCase() || ''}`}
                                </Text>
                            </Pressable>}
                       
                        {prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={async () => {
                                    // clearePrecentageList();
                                    // if (clearDownload)
                                    //     await clearDownload(file)

                                    cancelForFileInfo()
                                    // console.log(`\n\n\nthis  the cleare cencle ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: isFromSearch ? 3.6 : 3,
                                    position: 'relative'
                                }}
                            >

                                <View style={{ position: 'absolute' }} />
                                <XMarkIcon
                                    size={15}
                                    color={isCurrentUser ? theme.buttonBg : theme.centerChannelBg}
                                />
                            </Pressable>
                        }
                        {prcentage.some((item) => item.id === file.id) &&
                            <View style={{
                                position: 'absolute',
                                zIndex: 12, top: isFromSearch ? 16 : 2, start: isFromSearch ? undefined : 2.2
                            }}>
                                <CustomCircularProgress
                                    isFromSearch={isFromSearch || false}
                                    value={0.99} isCurrentUser={isCurrentUser ?? false} />

                            </View>}

                    </View>


                    }
                    {/* <View style={{
                        marginTop: 5,
                        marginBottom: 15,
                        marginStart: 22,
                    }}
                    >
                        {!isAudioEnabled && fileIcon}
                    </View> */}
                    {/* <View style={{ width: wrapperWidth - 50 }}> */}

                    {fileInfo}
                    {/* </View> */}
                    {/* {onOptionsPress &&
                    <View style={{
                        end: isAudio ? 55 : undefined,
                        bottom: isAudio ? 7 : undefined
                    }}>
                        <FileOptionsIcon
                            onPress={handleOnOptionsPress}
                            selected={optionSelected}
                        />
                    </View>
                } */}

                </View>
                {isFromSearch && <View

                    style={{
                        marginTop: 15,
                        width: '100%',
                        borderBottomWidth: isFromSearch ? 1 : undefined,
                        borderBottomColor: isFromSearch ? changeOpacity(theme.sidebarText, 0.15) : undefined,

                    }} />}
            </View>

        );
    };



    let fileComponent;
    if (isVideo(file)) {
        const renderVideoFile = (
            <TouchableWithoutFeedback
                disabled={isPressDisabled}
                onPress={onGestureEvent}
                // onPress={goToVedioPcreen}
                style={{
                    marginBottom: 15,
                    // backgroundColor:'red'
                }}

            >
                <Animated.View style={[
                    // styles, 
                    asCard ? style.imageVideo : null,
                    {
                        borderColor: isCurrentUser ? theme.buttonBg : theme.sidebarTextHoverBg, borderWidth: 3, borderRadius: 8,
                        marginBottom: isMenyFile ? 5 : 0,
                        marginTop: 0
                    }]}>
                    <VideoFile
                        isFromPostList={!isFromSearch}
                        file={file}
                        forwardRef={ref}
                        inViewPort={inViewPort}
                        isSingleImage={isSingleImage}
                        contentFit={'cover'}
                        wrapperWidth={wrapperWidth}
                        updateFileForGallery={updateFileForGallery}
                        index={index}
                    />
                    {/*Boolean(nonVisibleImagesCount) &&
                        <ImageFileOverlay
                            value={nonVisibleImagesCount}
                        />
                    */}
                </Animated.View>
            </TouchableWithoutFeedback>
        );

        fileComponent = asCard ? renderCardWithImage(renderVideoFile) : renderVideoFile;
    }
    else if (isAudio) {
        const touchableWithPreview = (
            <TouchableWithFeedback
                onPress={handlePreviewPress}
                // onPress={goToVedioPcreen}
                disabled={isPressDisabled}
                type={'opacity'}
            >

                <FileIcon
                    file={file}
                />
            </TouchableWithFeedback>
        );

        fileComponent = isFromSearch ? renderCardWithImage(touchableWithPreview) :
            <View
                style={{
                    // flex: 1, marginEnd: 55,
                    width: 290,
                    start: 0,
                    marginBottom: isMenyFile ? 5 : 0,
                

                }}>

                <AudioFile
                    key={file.id}
                    isCurrentUser={isCurrentUser ?? undefined}
                    acknolowgment={acknolowgmentAudio}
                    disabled={isPressDisabled}
                    file={file}
                    author={author}
                />
            </View>

    }
    else if (isImage(file)) {
        const renderImageFile = (
            <Pressable
                style={[{
                    overflow: 'hidden',
                    marginBottom: isMenyFile ? 5 : 0,
                    //  height:handleFileHeighAndWidth().height,width:handleFileHeighAndWidth().width,
                },]}
                onPress={onGestureEvent}

            //    onPress={goToVedioPcreen}

            >
                {currentPostTime && <View style={{
                    position: 'absolute',
                    borderRadius: 18,
                    backgroundColor: changeOpacity(theme.sidebarText, 0.50),
                    bottom: 10,
                    end: isCurrentUser ? '5%' : undefined,
                    start: isCurrentUser ? undefined : '5%',
                    zIndex: 14,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: 5
                }}>

                    <Text
                        style={{
                            color: 'white',
                            ...typography('Heading', 75, 'Regular'),
                        }}
                    >
                        {currentPostTime ? Time(new Date(currentPostTime)) : ''}
                    </Text>
                    {acknolowgmentImage && React.isValidElement(acknolowgmentImage) ? acknolowgmentImage : null}
                </View>}
                <View style={{
                    right: isHasMessage ? 10 : undefined,
                    borderRadius: isHasMessage ? 16 : undefined,
                    backgroundColor: theme.centerChannelBg,
                    margin: isMenyFile ? 5 : 0
                }}>
                    <ImageFile
                        isHasMessage={isHasMessage}
                        isCurrentUser={isCurrentUser}
                        isFromPostList={!isFromSearch}
                        file={file}
                        forwardRef={ref}
                        inViewPort={inViewPort}
                        isSingleImage={true}
                        contentFit={'contain'}
                        wrapperWidth={wrapperWidth}
                    />
                </View>
            </Pressable>
        )
        //     </TouchableWithoutFeedback>
        // );

        // المسؤولين
        fileComponent = asCard ? renderCardWithImage(renderImageFile) : renderImageFile;
    } else if (isDocument(file)) {
        const touchableWithPreview = (
            <TouchableWithFeedback
                // onPress={isSingleImage?onGestureEvent:goToVedioPcreen}
                //     onPress={goToVedioPcreen}
                disabled={isPressDisabled}
                type={'opacity'}
            >

                <FileIcon
                    file={file}
                />
            </TouchableWithFeedback>
        );

        fileComponent = isFromSearch ? renderCardWithImage(touchableWithPreview) :
            <View
                style={{
                    flexDirection: 'row',
                    marginBottom: isMenyFile ? 5 : 0,
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'

                }}
            >

                <View style={{ width: wrapperWidth - 58 }}>

                    <FileInfo
                        acknolowgmentFile={acknolowgmentFile}
                        isFromSearch={isFromSearch || false} disabled={isPressDisabled}
                        file={file}
                        showDate={showDate}
                        channelName={channelName}
                        onPress={handlePreviewPress}
                        isCurrentUser={isCurrentUser}
                    />
                </View>


                {file.localPath?.length === undefined ?
                    <View style={{
                        position: 'relative',
                        height: 44,
                        width: 44,
                        backgroundColor: !isCurrentUser ? theme.buttonBg : "white",
                        borderRadius: 25,
                        alignItems: 'center'
                        , justifyContent: 'center',
                        marginStart: 5,
                        marginEnd: 5,
                        end: isAudio ? 35 : 0,
                        top: !isFromSearch ? 2 : 0
                    }}>
                        {!prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={() => {
                                    // clearePrecentageList();
                                    startDownloadForFileInfo()
                                    // console.log(`\n\n\nthis the start download call ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: 3,

                                    position: 'relative'
                                }}
                            >

                                <View style={{ position: 'absolute' }} />
                                <ArrowDownTrayIcon
                                    size={24}
                                    color={
                                        isCurrentUser ?
                                            theme.buttonBg
                                            : "white"
                                    }
                                />
                            </Pressable>}
                        {prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={async () => {
                                    // clearePrecentageList();
                                    // if (clearDownload)
                                    //     await clearDownload(file)

                                    cancelForFileInfo()
                                    // console.log(`\n\n\nthis  the cleare cencle ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: 3,
                                    position: 'relative'
                                }}
                            >

                                <View style={{ position: 'absolute' }} />
                                <XMarkIcon
                                    size={24}
                                    color={isCurrentUser ? theme.buttonBg : "white"}
                                />
                            </Pressable>
                        }
                        {prcentage.some((item) => item.id === file.id) &&
                            <View style={{
                                position: 'absolute',
                                zIndex: 12, top: 2, start: 2.2
                            }}>
                                <CustomCircularProgress value={progress} isCurrentUser={isCurrentUser ?? false} />

                            </View>}

                    </View>
                    :


                    <Pressable

                        onPress={(e) => {
                            e.stopPropagation();
                            handlePreviewPress();
                        }}
                        style={{
                            height: 40,
                            width: 40,
                            borderRadius: 25, 
                            backgroundColor: !isCurrentUser ? theme.buttonBg : "white",
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginStart: 5,
                            marginEnd: 5,
                            marginTop: 5,


                        }}>
                        <FileIcon
                            iconSize={28}
                            isFromUpload={false}
                            backgroundColor={'transparent'}
                            iconColor={isCurrentUser ? theme.buttonBg : "white"}

                            file={file}
                        />
                    </Pressable>

                }
            </View>;

    } else {
        const touchableWithPreview = (
            <TouchableWithFeedback
                //   onPress={isSingleImage?onGestureEvent:goToVedioPcreen}
                /////     onPress={goToVedioPcreen}
                disabled={isPressDisabled}
                type={'opacity'}
            >

                <FileIcon
                    file={file}
                />
            </TouchableWithFeedback>
        );

        fileComponent = isFromSearch ? renderCardWithImage(touchableWithPreview) :
            <View
                style={{
                    flexDirection: 'row',
                    marginBottom: isMenyFile ? 5 : 0,
                    alignItems: 'center',
                    justifyContent: 'center'
                }}
            >

                <View style={{ width: wrapperWidth - 58 }}>

                    {
                        <FileInfo
                            acknolowgmentFile={acknolowgmentFile}
                            isFromSearch={isFromSearch || false} disabled={isPressDisabled}
                            file={file}
                            showDate={showDate}
                            channelName={channelName}
                            onPress={handlePreviewPress}
                            isCurrentUser={isCurrentUser}
                        />}
                </View>


                {file.localPath?.length === undefined ?
                    <View style={{
                        position: 'relative',
                        height: 44,
                        width: 44,
                        backgroundColor: !isCurrentUser ? theme.buttonBg : "white",
                        borderRadius: 25,
                        alignItems: 'center'
                        , justifyContent: 'center',
                        marginStart: 5,
                        marginEnd: 5,
                        top: !isFromSearch ? 2 : 0

                    }}>
                        {!prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={() => {
                                    // clearePrecentageList();
                                    startDownloadForFileInfo()
                                    // console.log(`\n\n\nthis the start download call ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: 3,

                                    position: 'relative'
                                }}
                            >

                                <View style={{ position: 'absolute' }} />
                                <ArrowDownTrayIcon
                                    size={24}
                                    color={isCurrentUser ? theme.buttonBg : "white"}
                                />
                            </Pressable>}
                        {prcentage.some((item) => item.id === file.id) &&
                            <Pressable
                                onPress={async () => {
                                    // clearePrecentageList();
                                    // if (clearDownload)
                                    //     await clearDownload(file)

                                    cancelForFileInfo()
                                    // console.log(`\n\n\nthis  the cleare cencle ${file.name} ${file.id} \n\n\n`)
                                    // setLastViewedFileInfo !== undefined && setLastViewedFileInfo(file)
                                }
                                }
                                style={{

                                    paddingBottom: 5,
                                    zIndex: 15,
                                    top: 3,
                                    position: 'relative'
                                }}
                            >

                                <View style={{ position: 'absolute' }} />
                                <XMarkIcon
                                    size={24}
                                    color={isCurrentUser ? theme.buttonBg : "white"}
                                />
                            </Pressable>
                        }
                        {prcentage.some((item) => item.id === file.id) &&
                            <View style={{
                                position: 'absolute',
                                zIndex: 12, top: 2, start: 2.2
                            }}>
                                <CustomCircularProgress value={progress} isCurrentUser={isCurrentUser ?? false} />

                            </View>}

                    </View>
                    :


                    <Pressable

                        onPress={(e) => {
                            e.stopPropagation();
                            handlePreviewPress();
                        }}
                        style={{
                            height: 40,
                            width: 40,
                            borderRadius: 25, backgroundColor: !isCurrentUser ? theme.buttonBg : "white",
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginStart: 5,
                            marginEnd: 5,
                            marginTop: 5,


                        }}>
                        <FileIcon
                            iconSize={28}
                            isFromUpload={false}
                            backgroundColor={'transparent'}
                            iconColor={isCurrentUser ? theme.buttonBg : "white"}

                            file={file}
                        />
                    </Pressable>

                }
            </View>;
    }
    return fileComponent;
};

export default File;
